# ������������ - miniRVָ�ʵ��
# ʹ�����Է�����λ�Ĵ������������
# 5���׶ε�״̬��ʵ��

.data
    # �������
    current_stage:  .word 0             # ��ǰ�׶� (0-4)
    random_seed:    .word 0             # ���������
    lfsr_state:     .word 0             # LFSR״̬
    random_array:   .word 0, 0, 0, 0, 0, 0, 0, 0  # 8��4bit���������
    sorted_flag:    .word 0             # ������ɱ�־

.text
.globl main

main:
    # ��ʼ��ջָ��
    lui sp, 0x10001

    # ��ʼ���Ĵ���
    addi s0, zero, 0        # s0 = current_stage
    addi s1, zero, 0        # s1 = random_seed
    addi s2, zero, 0        # s2 = lfsr_state

    # �����������ַ 0xFFFFF000
    lui s3, 0xFFFFF         # s3 = �������ַ

main_loop:
    # ��ȡ���뿪��״̬ (0xFFFFF070)
    lw t1, 0x70(s3)         # ��ȡ����״̬
    andi t1, t1, 3          # ȡ���2λ SW[1:0]

    # ���ݵ�ǰ�׶���ת
    beq s0, zero, stage0    # �׶�0
    addi t2, zero, 1
    beq s0, t2, stage1      # �׶�1
    addi t2, zero, 2
    beq s0, t2, stage2      # �׶�2
    addi t2, zero, 3
    beq s0, t2, stage3      # �׶�3
    addi t2, zero, 4
    beq s0, t2, stage4      # �׶�4
    jal zero, main_loop

# �׶�0����ʾ��ʱ��ֵ���ȴ�SW[1:0]==1
stage0:
    # ��ȡ����ʾ��ʱ��ֵ (0xFFFFF020)
    lw t0, 0x20(s3)         # ��ȡ��ʱ��ֵ
    sw t0, 0x00(s3)         # ��ʾ������� (0xFFFFF000)

    # ����Ƿ����׶�1
    addi t2, zero, 1
    beq t1, t2, enter_stage1
    jal zero, main_loop

enter_stage1:
    addi s0, zero, 1        # ����׶�1
    lw s1, 0x20(s3)         # ���浱ǰ��ʱ��ֵ��Ϊ����
    addi s2, s1, 0          # ��ʼ��LFSR״̬
    jal zero, main_loop

# �׶�1����ʾ����ֵ���ȴ�SW[1:0]==2
stage1:
    # ��ʾ����ֵ
    sw s1, 0x00(s3)         # ��ʾ���ӵ������

    # ����Ƿ����׶�2
    addi t2, zero, 2
    beq t1, t2, enter_stage2
    jal zero, main_loop

enter_stage2:
    addi s0, zero, 2        # ����׶�2
    jal zero, main_loop

# �׶�2�����ɲ���ʾ��������飬�ȴ�SW[1:0]==3
stage2:
    # ����8��4bit�����
    jal ra, generate_random_array

    # ����������ʾ�������
    jal ra, pack_array_for_display
    sw t0, 0x00(s3)         # ��ʾ�������

    # ����Ƿ����׶�3
    addi t2, zero, 3
    beq t1, t2, enter_stage3
    jal zero, main_loop

enter_stage3:
    addi s0, zero, 3        # ����׶�3
    jal zero, main_loop

# �׶�3���������飬�ȴ�SW[1:0]==0
stage3:
    # �������������
    jal ra, bubble_sort_array

    # ����LED[0]��ʾ������� (0xFFFFF060)
    addi t0, zero, 1
    sw t0, 0x60(s3)         # ����LED[0]

    # ����Ƿ����׶�4
    beq t1, zero, enter_stage4
    jal zero, main_loop

enter_stage4:
    addi s0, zero, 4        # ����׶�4
    jal zero, main_loop

# �׶�4����ʾ���������飬�������
stage4:
    # ��ʾ����������
    jal ra, pack_array_for_display
    sw t0, 0x00(s3)         # ��ʾ�������
    jal zero, stage4        # ������ʾ

# ����8��4bit������ĺ���
generate_random_array:
    addi sp, sp, -16
    sw ra, 12(sp)
    sw s7, 8(sp)
    sw s8, 4(sp)
    sw s9, 0(sp)

    lui s7, 0x10010         # �������ַ
    addi s7, s7, 32         # random_array��ַƫ��
    addi s8, zero, 0        # ѭ��������
    addi s9, zero, 8        # �����С

gen_loop:
    # ����LFSR���������
    jal ra, lfsr_next
    andi t0, t0, 15         # ȡ4bit (0-15)

    # �洢������
    slli t1, s8, 2          # ����ƫ���� (i*4)
    add t2, s7, t1          # �����ַ
    sw t0, 0(t2)            # �洢�����

    addi s8, s8, 1          # ������+1
    blt s8, s9, gen_loop    # ����ѭ��

    lw s9, 0(sp)
    lw s8, 4(sp)
    lw s7, 8(sp)
    lw ra, 12(sp)
    addi sp, sp, 16
    jalr zero, ra, 0

# ���Է�����λ�Ĵ��� (LFSR) ����
# ʹ��16λLFSR������ʽ: x^16 + x^14 + x^13 + x^11 + 1
lfsr_next:
    # ���㷴��λ
    srli t0, s2, 0          # bit 0
    srli t1, s2, 2          # bit 2
    srli t2, s2, 3          # bit 3
    srli t3, s2, 5          # bit 5

    andi t0, t0, 1
    andi t1, t1, 1
    andi t2, t2, 1
    andi t3, t3, 1

    xor t0, t0, t1
    xor t0, t0, t2
    xor t0, t0, t3          # ����λ

    # ���Ʋ����뷴��λ
    slli s2, s2, 1
    or s2, s2, t0
    # ����16λ - ʹ�ö���ָ��
    lui t4, 0x1
    addi t4, t4, -1         # t4 = 0xFFFF
    and s2, s2, t4

    # ��ֹȫ��״̬
    bne s2, zero, lfsr_done
    addi s2, zero, 1        # ���Ϊ0����Ϊ1

lfsr_done:
    addi t0, s2, 0          # ���ص�ǰLFSRֵ
    jalr zero, ra, 0

# ��������Ϊ��ʾ��ʽ�ĺ���
pack_array_for_display:
    addi sp, sp, -12
    sw ra, 8(sp)
    sw s7, 4(sp)
    sw s8, 0(sp)

    lui s7, 0x10010
    addi s7, s7, 32         # random_array��ַ
    addi t0, zero, 0        # �����ʼ��
    addi s8, zero, 0        # ѭ��������

pack_loop:
    slli t1, s8, 2          # ����ƫ����
    add t2, s7, t1          # �����ַ
    lw t3, 0(t2)            # ��ȡ����Ԫ��

    slli t1, s8, 2          # ÿ��Ԫ��ռ4λ
    sll t3, t3, t1          # ���Ƶ���ȷλ��
    or t0, t0, t3           # �ϲ������

    addi s8, s8, 1
    addi t4, zero, 8
    blt s8, t4, pack_loop

    lw s8, 0(sp)
    lw s7, 4(sp)
    lw ra, 8(sp)
    addi sp, sp, 12
    jalr zero, ra, 0

# ð��������
bubble_sort_array:
    addi sp, sp, -20
    sw ra, 16(sp)
    sw s7, 12(sp)
    sw s8, 8(sp)
    sw s9, 4(sp)
    sw s10, 0(sp)

    lui s7, 0x10010
    addi s7, s7, 32         # random_array��ַ
    addi s8, zero, 7        # ���ѭ�������� (n-1)

outer_loop:
    addi s9, zero, 0        # �ڲ�ѭ��������

inner_loop:
    # ���㵱ǰԪ�ص�ַ
    slli t1, s9, 2
    add t2, s7, t1
    lw t3, 0(t2)            # ��ǰԪ��
    lw t4, 4(t2)            # ��һ��Ԫ��

    # �Ƚϲ�����
    blt t3, t4, no_swap
    sw t4, 0(t2)            # ����
    sw t3, 4(t2)

no_swap:
    addi s9, s9, 1
    blt s9, s8, inner_loop

    addi s8, s8, -1
    blt zero, s8, outer_loop

    lw s10, 0(sp)
    lw s9, 4(sp)
    lw s8, 8(sp)
    lw s7, 12(sp)
    lw ra, 16(sp)
    addi sp, sp, 20
    jalr zero, ra, 0
