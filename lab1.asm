# ???????????? - miniRV??????
# ????????????????????????????
# 5??????????????

.data
    # ???????
    current_stage:  .word 0             # ?????? (0-4)
    random_seed:    .word 0             # ?????????
    lfsr_state:     .word 0             # LFSR??
    random_array:   .word 0, 0, 0, 0, 0, 0, 0, 0  # 8??4bit?????????
    sorted_flag:    .word 0             # ?????????

.text
.globl main

main:
    # ?????????
    lui sp, 0x10001

    # ??????????
    addi s0, zero, 0        # s0 = current_stage
    addi s1, zero, 0        # s1 = random_seed
    addi s2, zero, 0        # s2 = lfsr_state

    # ???????????? 0xFFFFF000
    lui s3, 0xFFFFF         # s3 = ????????

    # ????????????????
    addi t0, zero, 1        # ???????????1???????????????
    sw t0, 0x24(s3)         # ????????????0xFFFFF024

main_loop:
    # ??????????? (0xFFFFF070)
    lw t1, 0x70(s3)         # ?????????
    andi t1, t1, 3          # ????2?? SW[1:0]

    # ????????????
    beq s0, zero, stage0    # ???0
    addi t2, zero, 1
    beq s0, t2, stage1      # ???1
    addi t2, zero, 2
    beq s0, t2, stage2      # ???2
    addi t2, zero, 3
    beq s0, t2, stage3      # ???3
    addi t2, zero, 4
    beq s0, t2, stage4      # ???4
    jal zero, main_loop

# ???0????????????????SW[1:0]==1
stage0:
    # ??????????????
    # 0xFFFFF020: ????????? (??/??)
    lw t0, 0x20(s3)         # ???32????????
    sw t0, 0x00(s3)         # ?????????? (0xFFFFF000)

    # ???????????1
    addi t2, zero, 1
    beq t1, t2, enter_stage1
    jal zero, main_loop

enter_stage1:
    addi s0, zero, 1        # ??????1
    lw s1, 0x20(s3)         # ??0xFFFFF020????????????????????
    addi s2, s1, 0          # ?????LFSR??
    jal zero, main_loop

# ???1???????????????SW[1:0]==2
stage1:
    # ????????
    sw s1, 0x00(s3)         # ?????????????

    # ???????????2
    addi t2, zero, 2
    beq t1, t2, enter_stage2
    jal zero, main_loop

enter_stage2:
    addi s0, zero, 2        # ??????2
    jal zero, main_loop

# ???2??????????????????????SW[1:0]==3
stage2:
    # ????8??4bit?????
    jal ra, generate_random_array

    # ??????????????????
    jal ra, pack_array_for_display
    sw t0, 0x00(s3)         # ??????????

    # ???????????3
    addi t2, zero, 3
    beq t1, t2, enter_stage3

    # ??????????????main_loop??????????????
    jal zero, main_loop

enter_stage3:
    addi s0, zero, 3        # ??????3
    # ??sorted_flag???????????
    lui t5, 0x10010
    sw zero, 64(t5)         # sorted_flag = 0

    # ????LED???????????3
    sw zero, 0x60(s3)       # ??LED

    jal zero, main_loop

# ???3?????????????SW[1:0]==0
stage3:
    # ??????????????????????????????
    lui t5, 0x10010
    lw t6, 64(t5)           # ??sorted_flag
    bne t6, zero, stage3_wait  # ??????????????

    # ??????????????
    jal ra, bubble_sort_array

    # ????LED[0]?????????? (0xFFFFF060)
    addi t0, zero, 1
    sw t0, 0x60(s3)         # ????LED[0]

    # ??????????
    addi t0, zero, 1
    sw t0, 64(t5)           # ??sorted_flag = 1

stage3_wait:
    # ???????????4
    beq t1, zero, enter_stage4
    jal zero, main_loop

enter_stage4:
    addi s0, zero, 4        # ??????4
    jal zero, main_loop

# ???4??????????????????????
stage4:
    # ?????????????
    jal ra, pack_array_for_display
    sw t0, 0x00(s3)         # ??????????
    jal zero, stage4        # ???????

# ????8??4bit??????????
generate_random_array:
    addi sp, sp, -16
    sw ra, 12(sp)
    sw s7, 8(sp)
    sw s8, 4(sp)
    sw s9, 0(sp)

    lui s7, 0x10010         # ????????
    addi s7, s7, 32         # random_array??????
    addi s8, zero, 0        # ?????????
    addi s9, zero, 8        # ???????

gen_loop:
    # ????LFSR?????????
    jal ra, lfsr_next
    andi t0, t0, 15         # ?4bit (0-15)

    # ?????????
    slli t1, s8, 2          # ????????? (i*4)
    add t2, s7, t1          # ??????
    sw t0, 0(t2)            # ????????

    addi s8, s8, 1          # ??????+1
    blt s8, s9, gen_loop    # ???????

    lw s9, 0(sp)
    lw s8, 4(sp)
    lw s7, 8(sp)
    lw ra, 12(sp)
    addi sp, sp, 16
    jalr zero, ra, 0

# ???????????????? (LFSR) ????
# ???16??LFSR???????: x^16 + x^14 + x^13 + x^11 + 1
lfsr_next:
    # ????????
    srli t0, s2, 0          # bit 0
    srli t1, s2, 2          # bit 2
    srli t2, s2, 3          # bit 3
    srli t3, s2, 5          # bit 5

    andi t0, t0, 1
    andi t1, t1, 1
    andi t2, t2, 1
    andi t3, t3, 1

    xor t0, t0, t1
    xor t0, t0, t2
    xor t0, t0, t3          # ??????

    # ?????????????
    slli s2, s2, 1
    or s2, s2, t0
    # ????16?? - ?????????
    lui t4, 0x1
    addi t4, t4, -1         # t4 = 0xFFFF
    and s2, s2, t4

    # ????????
    bne s2, zero, lfsr_done
    addi s2, zero, 1        # ????0?????1

lfsr_done:
    addi t0, s2, 0          # ??????LFSR?
    jalr zero, ra, 0

# ????????????????????
pack_array_for_display:
    addi sp, sp, -12
    sw ra, 8(sp)
    sw s7, 4(sp)
    sw s8, 0(sp)

    lui s7, 0x10010
    addi s7, s7, 32         # random_array???
    addi t0, zero, 0        # ????????
    addi s8, zero, 0        # ?????????

pack_loop:
    slli t1, s8, 2          # ?????????
    add t2, s7, t1          # ??????
    lw t3, 0(t2)            # ??????????
    andi t3, t3, 15         # ????4??

    slli t1, s8, 2          # ????4???????i*4
    sll t3, t3, t1          # ????????????
    or t0, t0, t3           # ????????

    addi s8, s8, 1
    addi t4, zero, 8
    blt s8, t4, pack_loop

    lw s8, 0(sp)
    lw s7, 4(sp)
    lw ra, 8(sp)
    addi sp, sp, 12
    jalr zero, ra, 0

# ?????????
bubble_sort_array:
    addi sp, sp, -20
    sw ra, 16(sp)
    sw s7, 12(sp)
    sw s8, 8(sp)
    sw s9, 4(sp)
    sw s10, 0(sp)

    lui s7, 0x10010
    addi s7, s7, 32         # random_array???
    addi s8, zero, 7        # ???????????? (n-1)

outer_loop:
    addi s9, zero, 0        # ????????????

inner_loop:
    # ??????????
    slli t1, s9, 2
    add t2, s7, t1
    lw t3, 0(t2)            # ??????
    lw t4, 4(t2)            # ????????

    # ????????
    blt t3, t4, no_swap
    sw t4, 0(t2)            # ????
    sw t3, 4(t2)

no_swap:
    addi s9, s9, 1
    blt s9, s8, inner_loop

    addi s8, s8, -1
    blt zero, s8, outer_loop

    lw s10, 0(sp)
    lw s9, 4(sp)
    lw s8, 8(sp)
    lw s7, 12(sp)
    lw ra, 16(sp)
    addi sp, sp, 20
    jalr zero, ra, 0
